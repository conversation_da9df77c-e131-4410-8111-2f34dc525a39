{% extends 'base.html' %}

{% block title %}Sign Up with CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Customer Signup */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Professional Signup Section */
    .signup-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .signup-container {
        max-width: 700px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .signup-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .signup-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .signup-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="customer-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><path d="M12.5,5 Q17.5,10 12.5,15 Q7.5,10 12.5,5" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23customer-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .signup-header .content {
        position: relative;
        z-index: 2;
    }

    .signup-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .signup-title {
        font-family: var(--cw-font-display);
        font-size: 2.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .signup-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .signup-body {
        padding: 3rem;
    }

    /* Form Styling */
    .form-control-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
    }

    .form-label {
        color: var(--cw-neutral-700);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        margin-bottom: 0.5rem;
    }

    .form-label i {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        width: 100%;
        font-size: 1.125rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        width: 100%;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-ghost {
        border: 2px solid var(--cw-neutral-600);
        color: var(--cw-neutral-600);
        border-radius: 0.5rem;
        font-weight: 500;
        padding: 0.875rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-heading);
        width: 100%;
    }

    .btn-cw-ghost:hover {
        background: var(--cw-neutral-600);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-sm);
    }

    /* Alert Styling */
    .alert-cw {
        border-radius: 0.5rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid;
        font-family: var(--cw-font-primary);
    }

    .alert-cw-success {
        background: #f0fdf4;
        border-color: #bbf7d0;
        color: #166534;
    }

    .alert-cw-error {
        background: #fef2f2;
        border-color: #fecaca;
        color: #991b1b;
    }

    .alert-cw-info {
        background: #f0f9ff;
        border-color: #bae6fd;
        color: #0c4a6e;
    }

    /* Error Messages */
    .invalid-feedback {
        display: block !important;
        width: 100%;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #dc2626;
        font-weight: 500;
        font-family: var(--cw-font-primary);
    }

    .invalid-feedback i {
        margin-right: 0.25rem;
    }

    /* Form Text */
    .form-text {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        font-family: var(--cw-font-primary);
    }

    /* Password Toggle */
    .toggle-password {
        border: 2px solid var(--cw-brand-accent) !important;
        border-left: none !important;
        background: white !important;
        color: var(--cw-brand-primary) !important;
        padding: 0.875rem 1rem !important;
        border-radius: 0 0.5rem 0.5rem 0 !important;
        transition: all 0.2s ease;
    }

    .toggle-password:hover,
    .toggle-password:focus {
        background: var(--cw-accent-light) !important;
        border-color: var(--cw-brand-primary) !important;
        color: var(--cw-brand-primary) !important;
    }

    .input-group .form-control-cw:focus + .toggle-password {
        border-color: var(--cw-brand-primary) !important;
    }

    /* Checkbox Styling */
    .form-check-input {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.25rem;
        width: 1.25rem;
        height: 1.25rem;
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .form-check-label {
        color: var(--cw-neutral-700);
        font-family: var(--cw-font-primary);
        font-weight: 500;
        margin-left: 0.5rem;
    }

    /* Social Buttons */
    .social-btn {
        width: 50px;
        height: 50px;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 50%;
        background: white;
        color: var(--cw-neutral-600);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        font-size: 1.25rem;
    }

    .social-btn:hover {
        border-color: var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-sm);
    }

    .social-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* Divider */
    .divider {
        position: relative;
        text-align: center;
        margin: 2rem 0;
    }

    .divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--cw-brand-accent);
        z-index: 1;
    }

    .divider-text {
        background: white;
        padding: 0 1rem;
        color: var(--cw-neutral-600);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        position: relative;
        z-index: 2;
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Text Styling */
    .text-center p {
        color: var(--cw-neutral-600);
        font-family: var(--cw-font-primary);
        font-weight: 500;
    }

    .text-muted-cw {
        color: var(--cw-neutral-600);
        font-style: italic;
        font-size: 0.9rem;
    }

    /* Form Sections */
    .form-section {
        margin-bottom: 2.5rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .signup-section {
            padding: 3rem 0;
        }

        .signup-container {
            padding: 0 1.5rem;
        }

        .signup-header {
            padding: 2rem 2rem 1.5rem;
        }

        .signup-title {
            font-size: 2.25rem;
        }

        .signup-subtitle {
            font-size: 1.125rem;
        }

        .signup-body {
            padding: 2rem;
        }
    }

    @media (max-width: 576px) {
        .signup-container {
            padding: 0 1rem;
        }

        .signup-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .signup-body {
            padding: 1.5rem;
        }

        .signup-title {
            font-size: 1.875rem;
        }

        .signup-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="signup-section">
    <div class="signup-container">
        <div class="signup-card">
            <div class="signup-header">
                <div class="content">
                    <div class="signup-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h1 class="signup-title">Join CozyWish</h1>
                    <p class="signup-subtitle">Create your account to discover amazing venues</p>
                </div>
            </div>

            <div class="signup-body">
                <form method="post" novalidate>
                  {% csrf_token %}

                  {% if form.non_field_errors %}
                  <div class="alert-cw alert-cw-error mb-4">
                    {% for error in form.non_field_errors %}
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}

                  <!-- Account Information Section -->
                  <div class="form-section">
                    <!-- Email field -->
                    <div class="mb-4">
                      <label for="{{ form.email.id_for_label }}" class="form-label">
                        <i class="fas fa-envelope"></i>{{ form.email.label }}
                      </label>
                      {% if form.email.errors %}
                        {{ form.email|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your email address" }}
                      {% else %}
                        {{ form.email|add_class:"form-control-cw"|attr:"placeholder:Enter your email address" }}
                      {% endif %}
                      {% if form.email.errors %}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.email.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                      {% if form.email.help_text %}
                      <div class="form-text">{{ form.email.help_text }}</div>
                      {% endif %}
                    </div>

                    <!-- Password field -->
                    <div class="mb-4">
                      <label for="{{ form.password1.id_for_label }}" class="form-label">
                        <i class="fas fa-lock"></i>{{ form.password1.label }}
                      </label>
                      <div class="input-group">
                        {% if form.password1.errors %}
                          {{ form.password1|add_class:"form-control-cw is-invalid"|attr:"placeholder:Create a strong password" }}
                        {% else %}
                          {{ form.password1|add_class:"form-control-cw"|attr:"placeholder:Create a strong password" }}
                        {% endif %}
                        <button type="button" class="btn toggle-password" data-target="#{{ form.password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                          <i class="fas fa-eye" aria-hidden="true"></i>
                        </button>
                      </div>
                      {% if form.password1.errors %}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.password1.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                      {% if form.password1.help_text %}
                      <div class="form-text">{{ form.password1.help_text }}</div>
                      {% endif %}
                    </div>

                    <!-- Confirm Password field -->
                    <div class="mb-4">
                      <label for="{{ form.password2.id_for_label }}" class="form-label">
                        <i class="fas fa-lock"></i>{{ form.password2.label }}
                      </label>
                      <div class="input-group">
                        {% if form.password2.errors %}
                          {{ form.password2|add_class:"form-control-cw is-invalid"|attr:"placeholder:Confirm your password" }}
                        {% else %}
                          {{ form.password2|add_class:"form-control-cw"|attr:"placeholder:Confirm your password" }}
                        {% endif %}
                        <button type="button" class="btn toggle-password" data-target="#{{ form.password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                          <i class="fas fa-eye" aria-hidden="true"></i>
                        </button>
                      </div>
                      {% if form.password2.errors %}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.password2.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                      {% if form.password2.help_text %}
                      <div class="form-text">{{ form.password2.help_text }}</div>
                      {% endif %}
                    </div>

                    <!-- Terms checkbox -->
                    <div class="form-check mb-4">
                      {{ form.agree_to_terms|add_class:"form-check-input" }}
                      <label class="form-check-label" for="{{ form.agree_to_terms.id_for_label }}">
                        I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                      </label>
                      {% if form.agree_to_terms.errors %}
                      <div class="invalid-feedback" role="alert" aria-live="polite">
                        {% for error in form.agree_to_terms.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                    </div>

                    <!-- Submit button -->
                    <div class="d-grid mb-4">
                      <button type="submit" class="btn-cw-primary">
                        <i class="fas fa-user-plus me-2"></i>Create Account
                      </button>
                    </div>
                  </div>
                </form>

                <!-- Divider -->
                <div class="divider">
                  <span class="divider-text">or continue with</span>
                </div>

                <!-- Social signup buttons -->
                <div class="d-flex justify-content-center gap-3 mb-3">
                  <button type="button" class="social-btn" disabled>
                    <i class="fab fa-google"></i>
                  </button>
                  <button type="button" class="social-btn" disabled>
                    <i class="fab fa-apple"></i>
                  </button>
                  <button type="button" class="social-btn" disabled>
                    <i class="fab fa-facebook-f"></i>
                  </button>
                </div>
                <p class="text-center text-muted-cw mb-4">Social signup coming soon</p>

                <!-- Login and business links -->
                <div class="text-center">
                  <p class="mb-3">Already have an account?</p>
                  <div class="d-grid mb-4">
                    <a href="{% url 'accounts_app:customer_login' %}" class="btn-cw-secondary">
                      <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </a>
                  </div>

                  <p class="mb-3">Are you a service provider?</p>
                  <div class="d-grid">
                    <a href="{% url 'accounts_app:for_business' %}" class="btn-cw-ghost">
                      <i class="fas fa-store me-2"></i>For Business
                    </a>
                  </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Password Toggle JavaScript -->
<script>
(function() {
    'use strict';

    // Password toggle functionality
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();

            const targetSelector = toggleBtn.getAttribute('data-target');
            if (!targetSelector) return;

            const input = document.querySelector(targetSelector);
            if (!input) return;

            // Toggle password visibility
            const isPassword = input.type === 'password';
            input.type = isPassword ? 'text' : 'password';

            // Update icon
            const icon = toggleBtn.querySelector('i');
            if (icon) {
                if (isPassword) {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            // Update accessibility attributes
            const newTitle = isPassword ? 'Hide password' : 'Show password';
            toggleBtn.title = newTitle;
            toggleBtn.setAttribute('aria-label', newTitle);

            return false;
        }
    }, true);
})();
</script>
{% endblock %}
